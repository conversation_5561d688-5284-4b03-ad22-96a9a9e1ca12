<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class RichTextEditorInitialContentTest extends TestCase
{
    /*********************************************************************
     * TEST RICH TEXT EDITOR HANDLES INITIAL CONTENT
     *********************************************************************
     *
     * Verifies that the rich text editor component properly handles
     * initial content loading for wire:model.defer scenarios.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_handles_initial_content(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that initial value handling is present
        $this->assertStringContainsString('data_get($this, $modelAttribute', $content);
        $this->assertStringContainsString('initialValue', $content);
        $this->assertStringContainsString('addslashes', $content);
        
        // Assert that Alpine.js component accepts initialValue parameter
        $this->assertStringContainsString('(content, placeholder, disabled, initialValue)', $content);
        
        // Assert that initial content logic is implemented
        $this->assertStringContainsString('if (!initialContent && this.initialValue)', $content);
        $this->assertStringContainsString('this.content = initialContent', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR ESCAPES INITIAL CONTENT
     *********************************************************************
     *
     * Verifies that the rich text editor component properly escapes
     * initial content to prevent XSS and JavaScript injection.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_escapes_initial_content(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that content is properly escaped
        $this->assertStringContainsString('addslashes($initialValue)', $content);
        $this->assertStringContainsString('{!! $initialValue !!}', $content);
        
        // Assert that string validation is present
        $this->assertStringContainsString('is_string($initialValue)', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR CONTENT SYNCHRONIZATION
     *********************************************************************
     *
     * Verifies that the rich text editor component properly synchronizes
     * initial content with the Livewire model.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_content_synchronization(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that content synchronization logic is present
        $this->assertStringContainsString('this.content = initialContent', $content);
        $this->assertStringContainsString('getContentAsString', $content);
        
        // Assert that the editor content is updated
        $this->assertStringContainsString('this.$refs.editor.innerHTML = initialContent', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR HANDLES EMPTY INITIAL CONTENT
     *********************************************************************
     *
     * Verifies that the rich text editor component gracefully handles
     * empty or null initial content values.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_handles_empty_initial_content(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that empty content handling is present
        $this->assertStringContainsString('initialValue || \'\'', $content);
        $this->assertStringContainsString('if (!initialContent && this.initialValue)', $content);
        
        // Assert that null/undefined checks are in place
        $this->assertStringContainsString('if (value === null || value === undefined)', $content);
    }
}
