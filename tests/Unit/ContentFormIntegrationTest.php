<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class ContentFormIntegrationTest extends TestCase
{
    /*********************************************************************
     * TEST CONTENT FORM USES RICH TEXT EDITOR
     *********************************************************************
     *
     * Verifies that the content form has been updated to use the
     * rich text editor component instead of a plain textarea.
     *
     * @return void
     *
     *********************************************************************/
    public function test_content_form_uses_rich_text_editor(): void
    {
        $formPath = __DIR__ . '/../../resources/views/livewire/form/content-form.blade.php';
        $this->assertFileExists($formPath);
        
        $content = file_get_contents($formPath);
        
        // Assert that the form uses the rich text editor component
        $this->assertStringContainsString('<x-rich-text-editor', $content);
        $this->assertStringContainsString('wire:model.defer="content_body"', $content);
        $this->assertStringContainsString(':disabled="$disable_fields"', $content);
        $this->assertStringContainsString('id="content_body"', $content);
        
        // Assert that the old textarea element for content_body is no longer present
        $this->assertStringNotContainsString('<textarea @disabled($disable_fields) id="content_body"', $content);
        
        // Assert that error handling is still present
        $this->assertStringContainsString('@error(\'content_body\')', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR ICONS EXIST
     *********************************************************************
     *
     * Verifies that all the required Lucide icons for the rich text
     * editor toolbar exist in the codebase.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_icons_exist(): void
    {
        $iconsPath = __DIR__ . '/../../resources/views/components/icons/lucide';
        
        // Check that all required icons exist
        $requiredIcons = ['bold', 'italic', 'underline', 'list', 'list-ordered'];
        
        foreach ($requiredIcons as $icon) {
            $iconPath = $iconsPath . '/' . $icon . '.blade.php';
            $this->assertFileExists($iconPath, "Icon {$icon} should exist");
            
            // Verify the icon file contains SVG content
            $iconContent = file_get_contents($iconPath);
            $this->assertStringContainsString('<svg', $iconContent);
            $this->assertStringContainsString('lucide', $iconContent);
        }
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR COMPONENT STRUCTURE
     *********************************************************************
     *
     * Verifies that the rich text editor component has the correct
     * structure with toolbar and content area.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_component_structure(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert toolbar structure
        $this->assertStringContainsString('<!-- Toolbar -->', $content);
        $this->assertStringContainsString('<!-- Bold -->', $content);
        $this->assertStringContainsString('<!-- Italic -->', $content);
        $this->assertStringContainsString('<!-- Underline -->', $content);
        $this->assertStringContainsString('<!-- Bullet List -->', $content);
        $this->assertStringContainsString('<!-- Numbered List -->', $content);
        
        // Assert editor content area
        $this->assertStringContainsString('<!-- Editor Content -->', $content);
        $this->assertStringContainsString('contenteditable="true"', $content);
        $this->assertStringContainsString('rich-text-editor-content', $content);
        
        // Assert Alpine.js functionality
        $this->assertStringContainsString('toggleFormat', $content);
        $this->assertStringContainsString('toggleList', $content);
        $this->assertStringContainsString('handleKeydown', $content);
        $this->assertStringContainsString('updateContent', $content);
    }
}
