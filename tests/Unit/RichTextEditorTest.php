<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class RichTextEditorTest extends TestCase
{

    /*********************************************************************
     * TEST RICH TEXT EDITOR COMPONENT RENDERS
     *********************************************************************
     *
     * Verifies that the rich text editor component renders correctly
     * with proper attributes and structure.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_component_renders(): void
    {
        // Test that the component file exists
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $this->assertFileExists($componentPath);

        // Test that the component contains expected content
        $content = file_get_contents($componentPath);
        $this->assertStringContainsString('rich-text-editor-wrapper', $content);
        $this->assertStringContainsString('richTextEditor', $content);
        $this->assertStringContainsString('Alpine.data', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR WITH DISABLED STATE
     *********************************************************************
     *
     * Verifies that the rich text editor component handles disabled
     * state correctly.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_disabled_state(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);

        // Assert that disabled state handling is present
        $this->assertStringContainsString('disabled', $content);
        $this->assertStringContainsString('contenteditable', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR WITH WIRE MODEL
     *********************************************************************
     *
     * Verifies that the rich text editor component correctly handles
     * wire:model.defer attribute.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_wire_model(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);

        // Assert that wire model handling is present
        $this->assertStringContainsString('entangle', $content);
        $this->assertStringContainsString('.defer', $content);
        $this->assertStringContainsString('wire:model', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR JAVASCRIPT SETUP
     *********************************************************************
     *
     * Verifies that the rich text editor component includes the
     * necessary JavaScript setup function.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_javascript_setup(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);

        // Assert that JavaScript setup is included
        $this->assertStringContainsString('Alpine.data', $content);
        $this->assertStringContainsString('richTextEditor', $content);
        $this->assertStringContainsString('toggleFormat', $content);
        $this->assertStringContainsString('updateContent', $content);
        $this->assertStringContainsString('handleKeydown', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR CSS STYLES
     *********************************************************************
     *
     * Verifies that the rich text editor component includes the
     * necessary CSS styles for proper rendering.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_css_styles(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);

        // Assert that CSS styles are included
        $this->assertStringContainsString('.rich-text-editor-wrapper', $content);
        $this->assertStringContainsString('.rich-text-editor-content', $content);
        $this->assertStringContainsString('outline: none', $content);
        $this->assertStringContainsString('contenteditable', $content);
    }
}
