<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class RichTextEditorDisabledStateTest extends TestCase
{
    /*********************************************************************
     * TEST RICH TEXT EDITOR HIDES TOOLBAR WHEN DISABLED
     *********************************************************************
     *
     * Verifies that the rich text editor component hides the toolbar
     * when the editor is in disabled state.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_hides_toolbar_when_disabled(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that toolbar has x-show="!disabled" directive
        $this->assertStringContainsString('x-show="!disabled"', $content);
        
        // Assert that toolbar is properly marked
        $this->assertStringContainsString('<!-- Toolbar -->', $content);
        $this->assertStringContainsString('class="border border-gray-300 border-b-0 rounded-t-lg bg-gray-50 p-2" x-show="!disabled"', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR ADJUSTS STYLING WHEN DISABLED
     *********************************************************************
     *
     * Verifies that the rich text editor component adjusts the editor
     * content styling when disabled (rounded corners).
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_adjusts_styling_when_disabled(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that conditional styling is present
        $this->assertStringContainsString(':class="{', $content);
        $this->assertStringContainsString("'rounded-b-lg': !disabled", $content);
        $this->assertStringContainsString("'rounded-lg': disabled", $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR DISABLED CONTENTEDITABLE
     *********************************************************************
     *
     * Verifies that the rich text editor component properly sets
     * contenteditable based on disabled state.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_disabled_contenteditable(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that contenteditable is controlled by disabled state
        $this->assertStringContainsString('contenteditable="true"', $content);
        $this->assertStringContainsString(':contenteditable="!disabled"', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR DISABLED STATE HANDLING
     *********************************************************************
     *
     * Verifies that the rich text editor component properly handles
     * the disabled state in Alpine.js component.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_disabled_state_handling(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that disabled state is passed to Alpine.js
        $this->assertStringContainsString('disabled: disabled', $content);
        
        // Assert that disabled check is present in formatting functions
        $this->assertStringContainsString('if (this.disabled) return', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR TOOLBAR STRUCTURE
     *********************************************************************
     *
     * Verifies that the rich text editor component has the correct
     * toolbar structure with all formatting buttons.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_toolbar_structure(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that all toolbar buttons are present
        $this->assertStringContainsString('<!-- Bold -->', $content);
        $this->assertStringContainsString('<!-- Italic -->', $content);
        $this->assertStringContainsString('<!-- Underline -->', $content);
        $this->assertStringContainsString('<!-- Bullet List -->', $content);
        $this->assertStringContainsString('<!-- Numbered List -->', $content);
        
        // Assert that buttons use Lucide icons
        $this->assertStringContainsString('<x-icons.lucide.bold', $content);
        $this->assertStringContainsString('<x-icons.lucide.italic', $content);
        $this->assertStringContainsString('<x-icons.lucide.underline', $content);
        $this->assertStringContainsString('<x-icons.lucide.list', $content);
        $this->assertStringContainsString('<x-icons.lucide.list-ordered', $content);
    }
}
