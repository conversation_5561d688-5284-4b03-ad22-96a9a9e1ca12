<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class RichTextEditorModelTest extends TestCase
{
    /*********************************************************************
     * TEST RICH TEXT EDITOR ACCEPTS MODEL PROP
     *********************************************************************
     *
     * Verifies that the rich text editor component accepts an explicit
     * model prop for better initial content handling.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_accepts_model_prop(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that model prop is defined
        $this->assertStringContainsString("'model' => null", $content);
        
        // Assert that model prop is used in fallback logic
        $this->assertStringContainsString('$model', $content);
        $this->assertStringContainsString('?: $model', $content);
    }

    /*********************************************************************
     * TEST CONTENT FORM USES MODEL PROP
     *********************************************************************
     *
     * Verifies that the content form passes the explicit model prop
     * to the rich text editor component.
     *
     * @return void
     *
     *********************************************************************/
    public function test_content_form_uses_model_prop(): void
    {
        $formPath = __DIR__ . '/../../resources/views/livewire/form/content-form.blade.php';
        $content = file_get_contents($formPath);
        
        // Assert that model prop is passed
        $this->assertStringContainsString('model="content_body"', $content);
        
        // Assert that wire:model.defer is still present
        $this->assertStringContainsString('wire:model.defer="content_body"', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR MODEL ATTRIBUTE DETECTION
     *********************************************************************
     *
     * Verifies that the rich text editor component properly detects
     * the model attribute from various sources.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_model_attribute_detection(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that multiple detection methods are present
        $this->assertStringContainsString('$attributes->get(\'wire:model\')', $content);
        $this->assertStringContainsString('$attributes->get(\'wire:model.defer\')', $content);
        $this->assertStringContainsString('$wireAttributes = $attributes->getAttributes()', $content);
        
        // Assert that fallback logic exists
        $this->assertStringContainsString('if (!$modelAttribute)', $content);
        $this->assertStringContainsString('$wireModel ?: $wireModelDefer ?: $model', $content);
    }

    /*********************************************************************
     * TEST RICH TEXT EDITOR INITIAL VALUE EXTRACTION
     *********************************************************************
     *
     * Verifies that the rich text editor component properly extracts
     * initial values using the detected model attribute.
     *
     * @return void
     *
     *********************************************************************/
    public function test_rich_text_editor_initial_value_extraction(): void
    {
        $componentPath = __DIR__ . '/../../resources/views/components/rich-text-editor.blade.php';
        $content = file_get_contents($componentPath);
        
        // Assert that initial value extraction logic is present
        $this->assertStringContainsString('if (isset($this) && $modelAttribute)', $content);
        $this->assertStringContainsString('data_get($this, $modelAttribute', $content);
        $this->assertStringContainsString('addslashes($initialValue)', $content);
        
        // Assert that the initial value is passed to Alpine.js
        $this->assertStringContainsString('{!! $initialValue !!}', $content);
    }
}
