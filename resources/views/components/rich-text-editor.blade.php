@props([
    'wire:model' => null,
    'wire:model.defer' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'id' => null,
    'class' => '',
    'rows' => '10'
])

@php
    $wireModel = $attributes->get('wire:model');
    $wireModelDefer = $attributes->get('wire:model.defer');
    $modelAttribute = $wireModel ?: $wireModelDefer;
    $isDefer = !is_null($wireModelDefer);

    // Generate unique ID if not provided
    $editorId = $id ?: 'rich-text-editor-' . uniqid();
@endphp

<div
    x-data="richTextEditor(
        $wire.entangle('{{ $modelAttribute }}'){{ $isDefer ? '.defer' : '' }},
        '{{ $placeholder }}',
        {{ $disabled ? 'true' : 'false' }}
    )"
    x-init="initEditor()"
    wire:ignore
    {{ $attributes->except(['wire:model', 'wire:model.defer', 'placeholder', 'disabled', 'id', 'rows']) }}
    class="rich-text-editor-wrapper {{ $class }}"
>
    <!-- Toolbar -->
    <div class="border border-gray-300 border-b-0 rounded-t-lg bg-gray-50 p-2">
        <div class="flex flex-wrap items-center gap-1">
            <!-- Bold -->
            <button
                type="button"
                @click="toggleFormat('bold')"
                :class="{ 'bg-gray-200': isActive.bold }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bold"
            >
                <x-icons.lucide.bold class="w-4 h-4" />
            </button>

            <!-- Italic -->
            <button
                type="button"
                @click="toggleFormat('italic')"
                :class="{ 'bg-gray-200': isActive.italic }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Italic"
            >
                <x-icons.lucide.italic class="w-4 h-4" />
            </button>

            <!-- Underline -->
            <button
                type="button"
                @click="toggleFormat('underline')"
                :class="{ 'bg-gray-200': isActive.underline }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Underline"
            >
                <x-icons.lucide.underline class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Bullet List -->
            <button
                type="button"
                @click="toggleList('ul')"
                :class="{ 'bg-gray-200': isActive.ul }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bullet List"
            >
                <x-icons.lucide.list class="w-4 h-4" />
            </button>

            <!-- Numbered List -->
            <button
                type="button"
                @click="toggleList('ol')"
                :class="{ 'bg-gray-200': isActive.ol }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Numbered List"
            >
                <x-icons.lucide.list-ordered class="w-4 h-4" />
            </button>
        </div>
    </div>

    <!-- Editor Content -->
    <div
        x-ref="editor"
        id="{{ $editorId }}"
        contenteditable="true"
        :contenteditable="!disabled"
        @input="updateContent()"
        @keydown="handleKeydown($event)"
        @paste="handlePaste($event)"
        class="rich-text-editor-content border border-gray-300 rounded-b-lg min-h-[150px] p-3 text-sm text-gray-700 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 @error($modelAttribute) border-red-500 @enderror"
        :placeholder="placeholder"
        style="outline: none;"
    ></div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('richTextEditor', (content, placeholder, disabled) => ({
        content: typeof content === 'string' ? content : (content || ''),
        placeholder: placeholder,
        disabled: disabled,
        isActive: {
            bold: false,
            italic: false,
            underline: false,
            ul: false,
            ol: false
        },

        initEditor() {
            this.$nextTick(() => {
                // Set initial content - ensure it's a string
                const initialContent = this.getContentAsString(this.content);
                if (initialContent) {
                    this.$refs.editor.innerHTML = initialContent;
                }

                // Update active states
                this.updateActiveStates();

                // Watch for content changes
                this.$watch('content', (newContent) => {
                    const contentString = this.getContentAsString(newContent);
                    if (this.$refs.editor.innerHTML !== contentString) {
                        this.$refs.editor.innerHTML = contentString;
                    }
                });
            });
        },

        getContentAsString(value) {
            if (typeof value === 'string') {
                return value;
            }
            if (value === null || value === undefined) {
                return '';
            }
            // Handle Livewire proxy objects
            if (typeof value === 'object' && value.toString) {
                const stringValue = value.toString();
                return stringValue === '[object Object]' ? '' : stringValue;
            }
            return String(value);
        },

        updateContent() {
            // Ensure we're setting a string value
            const htmlContent = this.$refs.editor.innerHTML || '';
            // Only update if content has actually changed
            if (this.getContentAsString(this.content) !== htmlContent) {
                this.content = htmlContent;
            }
            this.updateActiveStates();
        },

        updateActiveStates() {
            this.isActive.bold = document.queryCommandState('bold');
            this.isActive.italic = document.queryCommandState('italic');
            this.isActive.underline = document.queryCommandState('underline');
            this.isActive.ul = document.queryCommandState('insertUnorderedList');
            this.isActive.ol = document.queryCommandState('insertOrderedList');
        },

        toggleFormat(command) {
            if (this.disabled) return;

            document.execCommand(command, false, null);
            this.updateContent();
            this.$refs.editor.focus();
        },

        toggleList(listType) {
            if (this.disabled) return;

            const command = listType === 'ul' ? 'insertUnorderedList' : 'insertOrderedList';
            document.execCommand(command, false, null);
            this.updateContent();
            this.$refs.editor.focus();
        },

        handleKeydown(event) {
            // Handle keyboard shortcuts
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'b':
                        event.preventDefault();
                        this.toggleFormat('bold');
                        break;
                    case 'i':
                        event.preventDefault();
                        this.toggleFormat('italic');
                        break;
                    case 'u':
                        event.preventDefault();
                        this.toggleFormat('underline');
                        break;
                }
            }

            // Update content after any keydown
            setTimeout(() => this.updateContent(), 0);
        },

        handlePaste(event) {
            // Allow paste but clean up content afterwards
            setTimeout(() => this.updateContent(), 0);
        }
    }));
});
</script>

<style>
/* Rich Text Editor Styling */
.rich-text-editor-wrapper {
    font-family: inherit;
}

.rich-text-editor-content {
    outline: none;
}

.rich-text-editor-content:empty:before {
    content: attr(placeholder);
    color: #9ca3af;
    pointer-events: none;
}

.rich-text-editor-content:focus:before {
    display: none;
}

/* List styling */
.rich-text-editor-content ul {
    list-style-type: disc;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor-content ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor-content li {
    margin: 0.25rem 0;
}

/* Bold, italic, underline styling */
.rich-text-editor-content strong,
.rich-text-editor-content b {
    font-weight: 600;
}

.rich-text-editor-content em,
.rich-text-editor-content i {
    font-style: italic;
}

.rich-text-editor-content u {
    text-decoration: underline;
}

/* Link styling */
.rich-text-editor-content a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Paragraph spacing */
.rich-text-editor-content p {
    margin-bottom: 0.5rem;
}

.rich-text-editor-content p:last-child {
    margin-bottom: 0;
}

/* Disabled state */
.rich-text-editor-content[contenteditable="false"] {
    background-color: #f9fafb;
    opacity: 0.6;
    cursor: not-allowed;
}
</style>
