@props([
    'wire:model' => null,
    'wire:model.defer' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'id' => null,
    'class' => '',
    'rows' => '10'
])

@php
    $wireModel = $attributes->get('wire:model');
    $wireModelDefer = $attributes->get('wire:model.defer');
    $modelAttribute = $wireModel ?: $wireModelDefer;
    $isDefer = !is_null($wireModelDefer);

    // Generate unique ID if not provided
    $editorId = $id ?: 'rich-text-editor-' . uniqid();
@endphp

<div
    x-data="richTextEditor(
        $wire.entangle('{{ $modelAttribute }}'){{ $isDefer ? '.defer' : '' }},
        '{{ $placeholder }}',
        {{ $disabled ? 'true' : 'false' }}
    )"
    x-init="initEditor()"
    wire:ignore
    {{ $attributes->except(['wire:model', 'wire:model.defer', 'placeholder', 'disabled', 'id', 'rows']) }}
    class="rich-text-editor-wrapper {{ $class }}"
>
    <!-- Toolbar -->
    <div class="border border-gray-300 border-b-0 rounded-t-lg bg-gray-50 p-2">
        <div class="flex flex-wrap items-center gap-1">
            <!-- Bold -->
            <button
                type="button"
                @click="toggleFormat('bold')"
                :class="{ 'bg-gray-200': isActive.bold }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bold"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 3v14h5.5c2.5 0 4.5-2 4.5-4.5 0-1.5-.8-2.8-2-3.5 1.2-.7 2-2 2-3.5C15 3 13 1 10.5 1H5v2zm2 2h3.5c1.4 0 2.5 1.1 2.5 2.5S11.9 10 10.5 10H7V5zm0 7h4c1.7 0 3 1.3 3 3s-1.3 3-3 3H7v-6z"/>
                </svg>
            </button>

            <!-- Italic -->
            <button
                type="button"
                @click="toggleFormat('italic')"
                :class="{ 'bg-gray-200': isActive.italic }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Italic"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8 1h8v2h-2.5l-3 12H13v2H5v-2h2.5l3-12H8V1z"/>
                </svg>
            </button>

            <!-- Underline -->
            <button
                type="button"
                @click="toggleFormat('underline')"
                :class="{ 'bg-gray-200': isActive.underline }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Underline"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 1C7.8 1 6 2.8 6 5v6c0 2.2 1.8 4 4 4s4-1.8 4-4V5c0-2.2-1.8-4-4-4zm2 11c0 1.1-.9 2-2 2s-2-.9-2-2V5c0-1.1.9-2 2-2s2 .9 2 2v7zM4 17h12v2H4v-2z"/>
                </svg>
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Bullet List -->
            <button
                type="button"
                @click="toggleList('ul')"
                :class="{ 'bg-gray-200': isActive.ul }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bullet List"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 6a2 2 0 100-4 2 2 0 000 4zM4 12a2 2 0 100-4 2 2 0 000 4zM4 18a2 2 0 100-4 2 2 0 000 4zM8 5h10v2H8V5zM8 11h10v2H8v-2zM8 17h10v2H8v-2z"/>
                </svg>
            </button>

            <!-- Numbered List -->
            <button
                type="button"
                @click="toggleList('ol')"
                :class="{ 'bg-gray-200': isActive.ol }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Numbered List"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4h1v1H3V4zm0 3h1v1H3V7zm0 3h1v1H3v-1zm0 3h1v1H3v-1zM7 4h11v2H7V4zm0 4h11v2H7V8zm0 4h11v2H7v-2zm0 4h11v2H7v-2z"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Editor Content -->
    <div
        x-ref="editor"
        id="{{ $editorId }}"
        contenteditable="true"
        :contenteditable="!disabled"
        @input="updateContent()"
        @keydown="handleKeydown($event)"
        @paste="handlePaste($event)"
        class="rich-text-editor-content border border-gray-300 rounded-b-lg min-h-[150px] p-3 text-sm text-gray-700 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 @error($modelAttribute) border-red-500 @enderror"
        :placeholder="placeholder"
        style="outline: none;"
    ></div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('richTextEditor', (content, placeholder, disabled) => ({
        content: content,
        placeholder: placeholder,
        disabled: disabled,
        isActive: {
            bold: false,
            italic: false,
            underline: false,
            ul: false,
            ol: false
        },

        initEditor() {
            this.$nextTick(() => {
                // Set initial content
                if (this.content) {
                    this.$refs.editor.innerHTML = this.content;
                }

                // Update active states
                this.updateActiveStates();

                // Watch for content changes
                this.$watch('content', (newContent) => {
                    if (this.$refs.editor.innerHTML !== newContent) {
                        this.$refs.editor.innerHTML = newContent || '';
                    }
                });
            });
        },

        updateContent() {
            this.content = this.$refs.editor.innerHTML;
            this.updateActiveStates();
        },

        updateActiveStates() {
            this.isActive.bold = document.queryCommandState('bold');
            this.isActive.italic = document.queryCommandState('italic');
            this.isActive.underline = document.queryCommandState('underline');
            this.isActive.ul = document.queryCommandState('insertUnorderedList');
            this.isActive.ol = document.queryCommandState('insertOrderedList');
        },

        toggleFormat(command) {
            if (this.disabled) return;

            document.execCommand(command, false, null);
            this.updateContent();
            this.$refs.editor.focus();
        },

        toggleList(listType) {
            if (this.disabled) return;

            const command = listType === 'ul' ? 'insertUnorderedList' : 'insertOrderedList';
            document.execCommand(command, false, null);
            this.updateContent();
            this.$refs.editor.focus();
        },

        handleKeydown(event) {
            // Handle keyboard shortcuts
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'b':
                        event.preventDefault();
                        this.toggleFormat('bold');
                        break;
                    case 'i':
                        event.preventDefault();
                        this.toggleFormat('italic');
                        break;
                    case 'u':
                        event.preventDefault();
                        this.toggleFormat('underline');
                        break;
                }
            }

            // Update content after any keydown
            setTimeout(() => this.updateContent(), 0);
        },

        handlePaste(event) {
            // Allow paste but clean up content afterwards
            setTimeout(() => this.updateContent(), 0);
        }
    }));
});
</script>

<style>
/* Rich Text Editor Styling */
.rich-text-editor-wrapper {
    font-family: inherit;
}

.rich-text-editor-content {
    outline: none;
}

.rich-text-editor-content:empty:before {
    content: attr(placeholder);
    color: #9ca3af;
    pointer-events: none;
}

.rich-text-editor-content:focus:before {
    display: none;
}

/* List styling */
.rich-text-editor-content ul {
    list-style-type: disc;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor-content ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor-content li {
    margin: 0.25rem 0;
}

/* Bold, italic, underline styling */
.rich-text-editor-content strong,
.rich-text-editor-content b {
    font-weight: 600;
}

.rich-text-editor-content em,
.rich-text-editor-content i {
    font-style: italic;
}

.rich-text-editor-content u {
    text-decoration: underline;
}

/* Link styling */
.rich-text-editor-content a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Paragraph spacing */
.rich-text-editor-content p {
    margin-bottom: 0.5rem;
}

.rich-text-editor-content p:last-child {
    margin-bottom: 0;
}

/* Disabled state */
.rich-text-editor-content[contenteditable="false"] {
    background-color: #f9fafb;
    opacity: 0.6;
    cursor: not-allowed;
}
</style>
