@props([
    'wire:model' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'id' => null,
    'class' => '',
])

@php
    $wireModel = $attributes->get('wire:model');
    $wireModelDefer = $attributes->get('wire:model.defer');
    $modelAttribute = $wireModel ?: $wireModelDefer;
    $isDefer = !is_null($wireModelDefer);
    
    // Generate unique ID if not provided
    $editorId = $id ?: 'rich-text-editor-' . uniqid();
@endphp

<div 
    x-data="richTextEditor(@entangle('{{ $modelAttribute }}'){{ $isDefer ? '.defer' : '' }}, '{{ $placeholder }}', {{ $disabled ? 'true' : 'false' }})"
    x-init="initEditor($refs.editor)"
    wire:ignore
    {{ $attributes->except(['wire:model', 'wire:model.defer', 'placeholder', 'disabled', 'id']) }}
    class="{{ $class }}"
>
    <div x-ref="editor" id="{{ $editorId }}"></div>
    
    <!-- Error display -->
    @error($modelAttribute)
        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
    @enderror
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('richTextEditor', (content, placeholder, disabled) => ({
        content: content,
        editor: null,
        
        initEditor(element) {
            // Import Tiptap dynamically
            import('@tiptap/vue-3').then(({ Editor }) => {
                import('@tiptap/starter-kit').then((StarterKit) => {
                    import('@tiptap/extension-placeholder').then((Placeholder) => {
                        import('@tiptap/extension-underline').then((Underline) => {
                            import('@tiptap/extension-link').then((Link) => {
                                this.editor = new Editor({
                                    element: element,
                                    extensions: [
                                        StarterKit.default,
                                        Underline.default,
                                        Link.default.configure({
                                            openOnClick: false,
                                        }),
                                        Placeholder.default.configure({
                                            placeholder: placeholder,
                                        }),
                                    ],
                                    content: this.content,
                                    editable: !disabled,
                                    onUpdate: ({ editor }) => {
                                        this.content = editor.getHTML();
                                    },
                                });
                                
                                // Watch for external content changes
                                this.$watch('content', (newContent) => {
                                    if (this.editor && this.editor.getHTML() !== newContent) {
                                        this.editor.commands.setContent(newContent, false);
                                    }
                                });
                            });
                        });
                    });
                });
            });
        },
        
        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        }
    }));
});
</script>

<style>
/* Basic Tiptap styling */
.ProseMirror {
    outline: none;
    min-height: 120px;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
}

.ProseMirror:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.ProseMirror.ProseMirror-focused {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

/* Placeholder styling */
.ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
}

/* List styling */
.ProseMirror ul {
    list-style-type: disc;
    margin-left: 1.5rem;
}

.ProseMirror ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
}

.ProseMirror li {
    margin: 0.25rem 0;
}

/* Link styling */
.ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Error state */
.border-red-500 .ProseMirror {
    border-color: #ef4444;
}

.border-red-500 .ProseMirror:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}
</style>
