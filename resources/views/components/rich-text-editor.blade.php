@props([
    'wire:model' => null,
    'wire:model.defer' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'id' => null,
    'class' => '',
    'rows' => '10'
])

@php
    $wireModel = $attributes->get('wire:model');
    $wireModelDefer = $attributes->get('wire:model.defer');
    $modelAttribute = $wireModel ?: $wireModelDefer;
    $isDefer = !is_null($wireModelDefer);
    
    // Generate unique ID if not provided
    $editorId = $id ?: 'rich-text-editor-' . uniqid();
    
    // Get the current value from Livewire
    $currentValue = data_get($this ?? null, $modelAttribute, '');
@endphp

<div 
    x-data="setupRichTextEditor(
        $wire.entangle('{{ $modelAttribute }}'){{ $isDefer ? '.defer' : '' }},
        '{{ $placeholder }}',
        {{ $disabled ? 'true' : 'false' }}
    )"
    x-init="() => init($refs.editor)"
    wire:ignore
    {{ $attributes->except(['wire:model', 'wire:model.defer', 'placeholder', 'disabled', 'id', 'rows']) }}
    class="rich-text-editor-wrapper {{ $class }}"
>
    <div x-ref="editor" id="{{ $editorId }}" class="rich-text-editor"></div>
</div>

<script>
window.setupRichTextEditor = function (content, placeholder, disabled) {
    let editor;

    return {
        content: content,

        async init(element) {
            // Dynamically import Tiptap modules
            const { Editor } = await import('@tiptap/vue-3');
            const StarterKit = await import('@tiptap/starter-kit');
            const Placeholder = await import('@tiptap/extension-placeholder');
            const Underline = await import('@tiptap/extension-underline');
            const Link = await import('@tiptap/extension-link');

            editor = new Editor({
                element: element,
                extensions: [
                    StarterKit.default,
                    Underline.default,
                    Link.default.configure({
                        openOnClick: false,
                    }),
                    Placeholder.default.configure({
                        placeholder: placeholder,
                    }),
                ],
                content: this.content,
                editable: !disabled,
                onUpdate: ({ editor }) => {
                    this.content = editor.getHTML();
                },
            });

            // Watch for external content changes
            this.$watch('content', (content) => {
                // If the new content matches Tiptap's then we just skip.
                if (content === editor.getHTML()) return;

                // Otherwise, update Tiptap's content
                editor.commands.setContent(content, false);
            });
        },

        destroy() {
            if (editor) {
                editor.destroy();
            }
        }
    };
};
</script>

<style>
/* Rich Text Editor Styling */
.rich-text-editor {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    overflow: hidden;
    background: white;
}

.rich-text-editor:focus-within {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.rich-text-editor .ProseMirror {
    outline: none;
    min-height: 150px;
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #374151;
}

.rich-text-editor .ProseMirror:focus {
    outline: none;
}

/* Placeholder styling */
.rich-text-editor .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
}

/* List styling */
.rich-text-editor .ProseMirror ul {
    list-style-type: disc;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor .ProseMirror ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.rich-text-editor .ProseMirror li {
    margin: 0.25rem 0;
}

/* Link styling */
.rich-text-editor .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Bold, italic, underline styling */
.rich-text-editor .ProseMirror strong {
    font-weight: 600;
}

.rich-text-editor .ProseMirror em {
    font-style: italic;
}

.rich-text-editor .ProseMirror u {
    text-decoration: underline;
}

/* Error state styling */
.border-red-500 .rich-text-editor {
    border-color: #ef4444;
}

.border-red-500 .rich-text-editor:focus-within {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Disabled state */
.rich-text-editor.disabled {
    background-color: #f9fafb;
    opacity: 0.6;
    cursor: not-allowed;
}

.rich-text-editor.disabled .ProseMirror {
    background-color: #f9fafb;
    cursor: not-allowed;
}
</style>
